<template>
	<view style="">
		<view class="mainPage" style="position: fixed;top:0;z-index: -1;width: 750rpx;height: 100%;"
			:style="mainBg?'':'background: linear-gradient(180deg, '+ themeColor.bg_main_color+'40'+' 0%, '+ '#F5F5F5 100%)'">
			<image v-if="mainBg" style="position: absolute;top: 0;right: 0;width: 100%;height: 100%;" :src="mainBg"
				mode=""></image>
		</view>
		<view class="" style="z-index:9;padding: 0 32rpx;width: 100%;"
			v-if="hotel&&hotelBill&&hotelBill&&hotelBill.room_number">
			<view class="" :style="{marginTop:searchBarTop + 'px','min-height':searchBarHeight + 'px',opacity:opacity}"
				style="display: flex;align-items: center;width: 100%;z-index: 9;position: relative;">
				<view class="" style="max-width: 430rpx;min-height: 76rpx;">
					<p style="font-size: 36rpx;" :style="{color:themeColor.text_main_color}">
						{{hotel?hotel.shop_name:''}}
					</p>
					<p style="font-size: 24rpx;" :style="{color:themeColor.text_main_color+'90'}">{{hotel.address}}</p>
				</view>
				<view class="" style="display: flex;align-items: flex-start;">
					<view class="icon-daohang" style="font-size: 40rpx;">

					</view>
					<p :style="{color:themeColor.text_main_color}" style="font-size: 20rpx;" @click="chooseLocation">导航
					</p>
				</view>
			</view>
		</view>

		<!-- 正常模式 -->
		<!-- 有订单的时候就一定有酒店数据 -->
		<view class="" :key="index" v-for="(item, index) in diyModel">
			<cardInfo v-if="hackReset&&item.sign=='hotel_info'" :styleModel="item.property.style"
				:billDetail="hotelBill">
			</cardInfo>
			<userInfoCard v-if="hackReset&&item.sign=='user_info'&&hotelBill&&hotelBill.room_number"
				:styleModel="item.property" :manInfo="authInfo">
			</userInfoCard>
			<roomCard v-if="hackReset&&item.sign=='room_card'" :styleModel="item.property" :billDetail="hotelBill">
			</roomCard>
			<authBanner :list="item.property" :billDetail="hotelBill"
				v-if="hackReset&&item.sign=='authentication'&&hotelBill&&(autoRoomSetting?!authInfo.authentication:false)&&hotelBill&&hotelBill.room_number">
			</authBanner>
			<hardware
				v-if="hackReset&&item.sign=='intelligent_hardware'&&hotelBill.bill_status==4&&(autoRoomSetting?authInfo.authentication:true)"
				:name="item.name" :list="item.property.list" :styleModel="item.property.style" :billDetail="hotelBill">
			</hardware>
			<roomServiceGrid
				v-if="hackReset&&item.sign=='room_service'&&hotelBill&&(autoRoomSetting?authInfo.authentication:true)&&hotelBill&&hotelBill.room_number"
				:billDetail="hotelBill" :name="item.name" :list="item.property.list" :styleModel="item.property.style"
				:code="sCode" @shareTo="toShare">
			</roomServiceGrid>
			<commonGride v-if="hackReset&&item.sign=='common_service'&&hotelBill&&hotelBill&&hotelBill.room_number"
				:billDetail="hotelBill" :name="item.name" :list="item.property.list" :styleModel="item.property.style">
			</commonGride>
			<couponDrag v-if="hackReset&&item.sign=='service'" :styleModel="item.property"></couponDrag>
			<codePop
				v-if="hackReset&&item.sign=='breakfast_coupon'&&hotelBill&&(autoRoomSetting?authInfo.authentication:true)&&hotelBill&&hotelBill.room_number"
				:styleModel="item.property" :billDetail="hotelBill">
			</codePop>
		</view>

		<!-- 多订单弹窗 -->
		<!-- 搜索到订单 -->
		<m-popup mode="bottom" :show="billShow">
			<view class="BillBox" v-if="billList.length > 0">
				<scroll-view scroll-y="true" style="height: 100%;width: 100%;">
					<p style="font-size: 34rpx;"><text class="icon-fengefu"
							:style="{color:themeColor.main_color}"></text>
						订单列表：请选择一个订单入住</p>
					<view class="billCard" v-for="(item,index) in billList" style="background:#c5c3ca59">
						<view class="" style="width: 80%;padding-right: 20rpx;line-height: 46rpx;">
							<!-- <view class="" style="display: flex;align-items: center;justify-content: space-between;">
								<text style="font-size: 28rpx;">订单号:{{item.bill_code}}</text>
							</view> -->
							<view class="" style="display: flex;align-items: center;justify-content: space-between;">
								<text>房间{{index+1}}:</text>
							</view>
							<view class="" style="display: flex;align-items: center;justify-content: space-between;">
								<text>房型:{{item.room_type_name}}</text>
								<text>{{item.room_number?'房间:'+item.room_number:'未排房'}}</text>
							</view>

							<view class="" style="display: flex;align-items: center;justify-content: space-between;">
								<text>{{formatPass(item.link_phone)}}</text>
								<text>{{item.enter_time_plan | moment}}</text>
							</view>
						</view>
						<view class="" style="width: 20%;display: flex;align-items: center;justify-content: center;">
							<view class=""
								style="height: 60rpx;width: fit-content;padding:10rpx 20rpx;border-radius: 30rpx;display: flex;align-items: center;justify-content: center;"
								:style="{'background':themeColor.main_color}">
								<text style="font-size: 24rpx;font-weight: 600;color: #FFFFFF;"
									@click="chooseRoom(item)">去选房</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</m-popup>

		<!-- 是否搜索 -->
		<!-- <m-popup mode="center" :show="showSearch">
			<view class="" style="height: 280rpx;width: 600rpx;display: flex;flex-direction: column;">
				<view class=""
					style="height: 50%;width: 100%;display: flex;align-items: center;justify-content: center;">
					<text>您当前没有预订/入住订单信息</text>
				</view>
				<view class=""
					style="height: 50%;width: 100%;display: flex;align-items: center;justify-content: center;"
					@click="toSearch">
					<view
						style="padding: 10rpx;width: 80%;display: flex;align-items: center;justify-content: center;color: darkgreen;border-radius: 30rpx;border: 1px solid darkgreen;">
						立即搜索</view>
				</view>
			</view>
		</m-popup> -->

		<!-- 是否脏房 -->
		<m-popup mode="center" :show="ifZang">
			<view class=""
				style="width: 650rpx;height: 500rpx;padding:30rpx;display: flex;flex-direction: column;align-items: center;">
				<p style="font-size: 44rpx;">温馨提示</p>
				<p style="margin-top: 40rpx;" v-if="ifZangCurrent == 1">你的房间正在清理中...</p>
				<p style="margin-top: 40rpx;" v-if="ifZangCurrent == 0">该房间正在打扫中、请联系前台</p>
				<view class="" v-if="ifZangCurrent == 1" @click="closeZang"
					style="margin-top: 60rpx;height: 60rpx;width: 400rpx;border-radius: 20rpx;background-color: darkseagreen;display: flex;align-items: center;justify-content: center;">
					<text style="color: #FFFFFF;">知道了</text>
				</view>
			</view>
		</m-popup>

		<!-- 二次确认 -->
		<m-popup :show="againSure" mode="center" :closeable="false">
			<view class="" style="height: 400rpx;width: 700rpx;border-radius: 20rpx;">
				<view class="" style="height: 260rpx;width: 100%;padding: 40rpx;">
					<view class="" style="display: flex;justify-content: center;">
						<p style="font-weight: 700;font-size: 44rpx;">提示</p>
					</view>
					<view class="" style="margin-top: 20rpx;">
						{{elseMan.name}}已同意您的邀请并支付了房费，是否同意！
					</view>
				</view>
				<view class=""
					style="height: 140rpx;width: 100%;display: flex;align-items: center;justify-content: space-around;">

					<view class="" @click="checkInAgain()"
						style="background : #5555ff;height: 80rpx;width: 240rpx;color: #FFFFFF;border-radius: 50rpx;display: flex;align-items: center;justify-content: center;">
						<text>同意同住</text>
					</view>
				</view>
			</view>
		</m-popup>

		<m-login v-if="hackReset1&&if_login" @loginTo="loginSucess" @closeToLogin="toCloseLogin"
			:customStyle="cusStyle"></m-login>
		<!-- 		<m-needAuthor v-if="hackReset&&!if_login"></m-needAuthor> -->
		<!-- 安全底部 -->
		<view class="" style="height: 140rpx;"></view>
		<m-tabbar :list="tabbar"></m-tabbar>
	</view>
</template>

<script>
	import cardInfo from './components/cardInfo.vue'
	import hardware from './components/hardware/hardware.vue'
	import roomServiceGrid from './components/roomServiceGrid.vue'
	import roomCard from './components/roomCard.vue'
	import commonGride from './components/commonGride.vue'
	import authBanner from './components/authBanner.vue'
	import userInfoCard from './components/userInfoCard.vue'
	import couponDrag from './components/couponDrag.vue'
	import codePop from './components/codePop.vue'

	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				diyModel: [],
				hotelBill: null,
				params: {
					search_word: '',
					bill_id: '',
					code: ''
				},
				hackReset: true,
				authInfo: null,
				autoRoomSetting: false,
				billShow: false,
				billList: [],
				code: '',
				bill_id: '',
				share_code: '',
				hackReset1: true,
				if_login: false,
				shareSign: '',

				cusStyle: null,
				team_id: '',
				cashAll: '', //费用相关
				sCode: '', //传给子组件的code,只有code存在才弹出分享
				ifZang: false,
				ifZangCurrent: 0,
				navBarHeight: 0,
				searchBarTop: 0,
				searchBarHeight: 0,
				menuButtonInfo: null,
				mainBg: '',
				timeInfo: null, //查询订单定时器
				elseMan: null,
				againSure:false
			};
		},
		components: {
			roomCard,
			cardInfo,
			hardware,
			roomServiceGrid,
			commonGride,
			authBanner,
			userInfoCard,
			couponDrag,
			codePop
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting', 'hotelList', 'setting'])
		},
		watch: {
			hotelBill: {
				handler(newVal, oldVal) {


				},
				immediate: false,
				deep: true
			}
		},
		async onLaunch() {
			await this.$onLaunched;
			const _this = this;
			setTimeout(() => {
				const systemInfo1 = wx.getSystemInfoSync();
				let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
				this.menuButtonInfo = menuButtonInfo
				this.searchBarTop = menuButtonInfo.top;
				this.searchBarHeight = menuButtonInfo.height;
				this.navBarHeight = systemInfo1.statusBarHeight + 44;
			}, 400);
		},
		async onReady() {
			await this.$onLaunched;
			const _this = this;
			setTimeout(() => {
				const systemInfo1 = wx.getSystemInfoSync();
				let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
				this.menuButtonInfo = menuButtonInfo
				this.searchBarTop = menuButtonInfo.top;
				this.searchBarHeight = menuButtonInfo.height;
				this.navBarHeight = systemInfo1.statusBarHeight + 44;
			}, 400);
		},
		async onLoad(options) {
			await this.$onLaunched;
			this.$iBox.http('getHomePageUi', {
				path: 'pages/index/index',
				shop_id: this.hotel.id
			})({
				method: 'post'
			}).then(res => {
				uni.hideLoading()
				// 判断是否是选择城市模式，判断组件
				res.data.forEach(item => {
					if (item.sign == 'book_room_1') {
						if (item.property.style == 4) {
							this.getCityModel(true)
						} else {
							this.getCityModel(false)
						}
					}
				})
			})
			
			this.getWxAuthorizeLocation()
			const systemInfo1 = wx.getSystemInfoSync();
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.menuButtonInfo = menuButtonInfo
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.navBarHeight = systemInfo1.statusBarHeight + 44;
			
			// 获取设置
			let check_buletooth = this.shopSetting.filter(item => {
				return item.sign == 'check_buletooth'
			})[0].property.status
			
			if (check_buletooth) {
			
				const systemSetting = wx.getSystemSetting()
				const systemInfo = wx.getDeviceInfo()
				const appAuth = wx.getAppAuthorizeSetting()
				console.log(systemSetting, appAuth, 'lk');
				if (!systemSetting.bluetoothEnabled) {
					uni.showModal({
						title: '太着急啦',
						content: '请打开手机蓝牙！',
						showCancel: false,
						success: (res) => {
			
						}
					})
			
				}
			
				if (!systemSetting.locationEnabled) {
					uni.showModal({
						title: '太着急啦',
						content: '请打开系统定位服务！',
						showCancel: false
					})
			
				}
			
				if (appAuth.bluetoothAuthorized != 'authorized' && systemInfo.system.includes('iOS')) {
					uni.showModal({
						title: '授权失败',
						content: '请允许微信使用蓝牙权限',
						showCancel: false
					})
			
				}
			
			
				if (appAuth.cameraAuthorized != 'authorized') {
					uni.showModal({
						title: '授权失败',
						content: '请允许微信使用手机的摄像头权限',
						showCancel: false
					})
			
				}
			
			
				if (appAuth.locationAuthorized != 'authorized') {
					uni.showModal({
						title: '授权失败',
						content: '请允许微信使用手机的定位权限',
						showCancel: false
					})
			
				}
			}
		},
		async onShow() {
			await this.$onLaunched;
			

			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})

			this.hackReset1 = false
			this.$nextTick(() => {
				this.hackReset1 = true
			})
			this.cusStyle = {
				zindex: 1001
			}

			// 设置标题
			let pages = getCurrentPages();
			this.pageUrl = pages[pages.length - 1].route;
			this.tabbar.forEach(item => {
				if (item.path == this.pageUrl) {
					uni.setNavigationBarTitle({
						title: item.name
					})
				}
			})

			//是否是会员
			wx.onMemoryWarning((res) => {
				console.log('onMemoryWarningReceive', res)
			})
			// 小程序二维码带参数,
			let scene = wx.getEnterOptionsSync()
			console.log(scene, 'scene');

			//是否是会员
			let set = this.setting.filter(item => {
				return item.sign == 'auto_register_member'
			})
			console.log(set[0].property,'ooop');
			if (set[0].property) {
				let a = set[0].property.value
				if (a == 2) {
					if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
						.upgrade_growth_value > -
						1) {
						this.if_login = false

						if (scene.query.scene) {
							// 扫码场景
							let query = decodeURIComponent(scene.query.scene)
							console.log(query, 'query', this.$iBox.linkFormat(query, "b"), this.$iBox.linkFormat(query,
								"c"));
							//解析参数
							if (query.includes("c=") && !query.includes("tc=")) {
								this.bill_id = this.$iBox.linkFormat(query, "b")
								this.code = this.$iBox.linkFormat(query, "c")
								this.$iBox.http('bindRoomBill', {
									bill_id: this.bill_id,
									code: this.code
								})({
									method: 'post'
								}).then(res => {
									if (res.code == 0) {
										this.params.bill_id = this.bill_id
										this.params.code = this.code
										this.showMt()
									}
								}).catch(err => {
									uni.showModal({
										title: '提示',
										content: err,
										showCancel: false,
										confirmText: '回到首页',
										success: res => {
											uni.reLaunch({
												url: '/pages/index/index'
											})
										}
									})
								})

							} else {
								this.params.bill_id = this.bill_id
								this.params.code = this.code

								this.showMt()
							}

						} else if (scene.query.c) {
							//点击链接场景
							console.log(scene, 'g');
							this.bill_id = scene.query.b
							this.code = scene.query.c
							this.$iBox.http('bindRoomBill', {
								bill_id: this.bill_id,
								code: this.code
							})({
								method: 'post'
							}).then(res => {
								if (res.code == 0) {
									this.params.bill_id = this.bill_id
									this.params.code = this.code
									this.showMt()
								}
							}).catch(err => {
								uni.showModal({
									title: '提示',
									content: err,
									showCancel: false,
									confirmText: '回到首页',
									success: res => {
										uni.reLaunch({
											url: '/pages/index/index'
										})
									}
								})
							})

						} else {
							this.params.bill_id = this.bill_id
							this.params.code = this.code
							this.showMt()
						}

					} else {
						this.showMt()
						this.if_login = true

					}
				} else if (a == 1) {
					// this.pop = true
					if (this.userInfo.phone) {
						this.if_login = false

						if (scene.query.scene) {
							// 扫码场景
							let query = decodeURIComponent(scene.query.scene)
							console.log(query, 'query', this.$iBox.linkFormat(query, "b"), this.$iBox.linkFormat(query,
								"c"));
							//解析参数
							if (query.includes("c=") && !query.includes("tc=")) {
								this.bill_id = this.$iBox.linkFormat(query, "b")
								this.code = this.$iBox.linkFormat(query, "c")
								this.$iBox.http('bindRoomBill', {
									bill_id: this.bill_id,
									code: this.code
								})({
									method: 'post'
								}).then(res => {
									if (res.code == 0) {
										this.params.bill_id = this.bill_id
										this.params.code = this.code
										this.showMt()
									}
								}).catch(err => {
									uni.showModal({
										title: '提示',
										content: err,
										showCancel: false,
										confirmText: '回到首页',
										success: res => {
											uni.reLaunch({
												url: '/pages/index/index'
											})
										}
									})
								})

							} else {
								this.params.bill_id = this.bill_id
								this.params.code = this.code

								this.showMt()
							}

						} else if (scene.query.c) {
							//点击链接场景
							console.log(scene, 'g');
							this.bill_id = scene.query.b
							this.code = scene.query.c
							this.$iBox.http('bindRoomBill', {
								bill_id: this.bill_id,
								code: this.code
							})({
								method: 'post'
							}).then(res => {
								if (res.code == 0) {
									this.params.bill_id = this.bill_id
									this.params.code = this.code
									this.showMt()
								}
							}).catch(err => {
								uni.showModal({
									title: '提示',
									content: err,
									showCancel: false,
									confirmText: '回到首页',
									success: res => {
										uni.reLaunch({
											url: '/pages/index/index'
										})
									}
								})
							})

						} else {
							this.params.bill_id = this.bill_id
							this.params.code = this.code
							this.showMt()
						}

					} else {
						this.if_login = true

					}
				}
			}
			console.log(this.hotelBill && this.hotelBill.team_id, 'hotelbill');

		},
		methods: {
			...mapActions('room', ['getHardWareList', 'getBillDetail', 'getRoomBillUser']),
			...mapActions('hotel', ['getHotel', 'getCityModel']),
			toCloseLogin() {
				// uni.showModal({
				// 	title: '提示！',
				// 	content: '为了获得更完整的会员服务请您授权您的手机号！',
				// 	showCancel: false,
				// 	success: res => {
				// 		this.hackReset1 = false
				// 		this.$nextTick(() => {
				// 			this.hackReset1 = true
				// 		})
				// 		this.if_login = true
				// 	}
				// })
			},
			getTeamInfo(params) {
				this.$iBox
					.http('getTeamRoomBillInfo', params)({
						method: 'post'
					})
					.then(res => {
						// 获取另外一人的信息
						this.elseMan = res.data.users.filter(item => {
							return item.common_code != this.userInfo.common_code
						})[0]
						console.log(this.elseMan,'this.elseMan');
						if (this.elseMan&&this.elseMan.confirm == 1) {
							this.againSure = true
						}
					})
			},
			checkInAgain() {
				this.$iBox.http('confirmSame', {
					team_id: this.hotelBill.team_id,
					confirm: 2
				})({
					method: 'post'
				}).then(res => {
					clearInterval(this.timeInfo)
					uni.showModal({
						title: '提示',
						content: '您已同意,请等待对方办理!',
						showCancel:false,
						success:(res)=> {
							this.againSure = false
						}
					})
				})
			},
			toSearch() {
				uni.navigateTo({
					url: '/packageA/autoRoom/searchPage/searchPage'
				})
			},
			loginSucess() {
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true

					let set = this.setting.filter(item => {
						return item.sign == 'auto_register_member'
					})
					if (set[0]&&set[0].property) {
						let a = set[0].property.value
						if (a == 2) {
							if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
								.upgrade_growth_value > -1) {
								this.if_login = false
								let scene = wx.getEnterOptionsSync()
								if (scene.query.scene) {
									let query = decodeURIComponent(scene.query.scene)
									//解析参数
									if (query.includes("c=") && !query.includes("tc=")) {
										this.bill_id = this.$iBox.linkFormat(query, "b")
										this.code = this.$iBox.linkFormat(query, "c")
										this.$iBox.http('bindRoomBill', {
											bill_id: this.bill_id,
											code: this.code
										})({
											method: 'post'
										}).then(res => {
											if (res.code == 0) {
												this.params.bill_id = this.bill_id
												this.params.code = this.code
												this.showMt()
											}
										}).catch(err => {
											uni.showModal({
												title: '提示',
												content: err,
												showCancel: false,
												confirmText: '回到首页',
												success: res => {
													uni.reLaunch({
														url: '/pages/index/index'
													})
												}
											})
										})

									} 

								} else if (scene.query.c) {
									this.bill_id = scene.query.b
									this.code = scene.query.c
									this.$iBox.http('bindRoomBill', {
										bill_id: this.bill_id,
										code: this.code
									})({
										method: 'post'
									}).then(res => {
										if (res.code == 0) {
											this.params.bill_id = this.bill_id
											this.params.code = this.code
											this.showMt()
										}
									}).catch(err => {
										uni.showModal({
											title: '提示',
											content: err,
											showCancel: false,
											confirmText: '回到首页',
											success: res => {
												uni.reLaunch({
													url: '/pages/index/index'
												})
											}
										})
									})

								} else {
									this.params.bill_id = this.bill_id
									this.params.code = this.code
									this.showMt()
								}
							} else {
								this.if_login = true
							}

						} else if (a == 1) {
							// this.pop = true
							if (this.userInfo.phone) {
								this.if_login = false
								let scene = wx.getEnterOptionsSync()
								if (scene.query.scene) {
									let query = decodeURIComponent(scene.query.scene)
									//解析参数
									if (query.includes("c=") && !query.includes("tc=")) {
										this.bill_id = this.$iBox.linkFormat(query, "b")
										this.code = this.$iBox.linkFormat(query, "c")
										this.$iBox.http('bindRoomBill', {
											bill_id: this.bill_id,
											code: this.code
										})({
											method: 'post'
										}).then(res => {
											if (res.code == 0) {
												this.params.bill_id = this.bill_id
												this.params.code = this.code
												this.showMt()
											}
										}).catch(err => {
											uni.showModal({
												title: '提示',
												content: err,
												showCancel: false,
												confirmText: '回到首页',
												success: res => {
													uni.reLaunch({
														url: '/pages/index/index'
													})
												}
											})
										})

									} 

								} else if (scene.query.c) {
									this.bill_id = scene.query.b
									this.code = scene.query.c
									this.$iBox.http('bindRoomBill', {
										bill_id: this.bill_id,
										code: this.code
									})({
										method: 'post'
									}).then(res => {
										if (res.code == 0) {
											this.params.bill_id = this.bill_id
											this.params.code = this.code
											this.showMt()
										}
									}).catch(err => {
										uni.showModal({
											title: '提示',
											content: err,
											showCancel: false,
											confirmText: '回到首页',
											success: res => {
												uni.reLaunch({
													url: '/pages/index/index'
												})
											}
										})
									})

								} else {
									this.params.bill_id = this.bill_id
									this.params.code = this.code
									this.showMt()
								}
							} else {
								this.if_login = true
							}
						}
					}
				})

			},
			chooseLocation() {
				wx.openLocation({
					latitude: Number(this.hotel.latitude), //维度
					longitude: Number(this.hotel.longitude), //经度
					name: this.hotel.shop_name, //目的地定位名称
					scale: 15, //缩放比例
					address: this.hotel.address, //导航详细地址
					fail: err => {
						console.log(err);
					}
				})
			},
			pageUI(e) {
				this.$iBox.http('getHomePageUi', {
					path: 'pages/myRoom/myRoom',
					shop_id: e
				})({
					method: 'post'
				}).then(res => {
					// this.hackReset = false
					// this.$nextTick(() => {
					// 	this.hackReset = true
					// })
					this.diyModel = res.data
					console.log(res.data,'res.data');
				})

			},
			formatPass(e) {
				return e.substring(0, 3) + '****' + e.substring(7, 11)
			},
			closeZang() {
				this.ifZang = false
			},
			chooseCity() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			changeHotel() {
				uni.navigateTo({
					url: '/pages/hotelList/hotelList?type=city'
				})
			},
			searchBill() {
				uni.navigateTo({
					url: '/packageA/autoRoom/searchPage/searchPage'
				})
			},
			toShare(e) {
				console.log(e);
				this.shareSign = e.shareSign
				this.share_code = e.share_code
				this.team_id = e.team_id
			},
			chooseRoom(e) {
				this.$iBox.http('getRoomBillInfo', {
					bill_id: e.id
				})({
					method: 'post'
				}).then(res1 => {
					let detail = null
					this.hotelBill = res1.data

					if (this.hotelBill && this.hotelBill.clean_status != 1) {
						this.ifZang = true
						if (this.hotelBill.dirty_check_in == 0) {
							this.ifZangCurrent = 0
						} else {
							this.ifZangCurrent = 1
						}

					} else {
						this.ifZang = false
					}

					this.getBillDetail(res1.data)
					this.$iBox.http('getRoomBillUser', {
						bill_id: e.id
					})({
						method: 'post'
					}).then(res2 => {

						if (res2.data.length > 0) {
							this.authInfo = res2.data.filter(item => {
								return item.common_code == this.userInfo.common_code
							})[0]

							this.getRoomBillUser(res2.data)

						} else {
							this.authInfo = null
							this.getRoomBillUser([])
						}


						this.billShow = false
						uni.navigateTo({
							url: '/packageA/autoRoom/chooseRoom/chooseRoom'
						})
						uni.hideLoading()
					})

				})
			},
			showMt() {
				// 获取自助入住设置，
				this.autoRoomSetting = this.shopSetting.filter(item => {
					return item.sign == 'self_check_in'
				})[0].property.status

				//首先判断是否开启自助入住
				// 开启自助入住
				console.log(this.autoRoomSetting, '自主入住');
				if (this.autoRoomSetting) {
					// 查询订单
					// uni.showLoading({
					// 	title: '正在加载...'
					// })

					this.$iBox.http('getUserRoomBillList', this.params)({
						method: 'post'
					}).then(res => {

						if (res.data && res.data != 'no_login_bill') {

							// 首先判断是否是本店订单
							let billList = res.data
							if (billList.length == 0) {
								let shop_id = this.hotel.id
								this.hotelBill = ''
								console.log(this.hotelBill, '是否还有订单');
								this.pageUI(shop_id)
								uni.hideLoading()
								this.hackReset = false
								this.$nextTick(() => {
									this.hackReset = true
								})

							} else if (billList.length == 1) { // 2.如果有订单，则判断订单数，如果订单是1个则正常显示判断是否认证
								// 自动切换到有订单的酒店
								let hotelChoose = this.hotelList.filter(item => {
									return item.id == billList[0].shop_id
								})[0]

								if (!hotelChoose) {
									uni.showModal({
										title: '提示',
										content: '此订单酒店已经下架',
										showCancel: false,
										success: res => {
											if (res.confirm) {
												this.getHotel({})
											}
										}
									})
								} else {
									this.getHotel(hotelChoose)
								}

								// 查询订单详情
								this.$iBox.http('getRoomBillInfo', {
									bill_id: billList[0].id
								})({
									method: 'post'
								}).then(res1 => {
									let detail = null
									this.hotelBill = res1.data

									if (this.hotelBill && this.hotelBill.team_id) {
										let self = this
										this.timeInfo && clearInterval(this.timeInfo)
										this.getTeamInfo({team_id:this.hotelBill.team_id}); // 立即执行第一次
										this.timeInfo = setInterval(() => {
											this.getTeamInfo({team_id:this.hotelBill.team_id});
										}, 5000);
									}

									if (this.hotelBill && this.hotelBill.clean_status != 1) {
										this.ifZang = true
										if (this.hotelBill.dirty_check_in == 0) {
											this.ifZangCurrent = 0
										} else {
											this.ifZangCurrent = 1
										}

									} else {
										this.ifZang = false
									}

									this.getBillDetail(res1.data)
									let shop_id = this.hotelBill.shop_id
									this.getHardWareList(res1.data.hardware_list)
									this.pageUI(shop_id)

									this.$iBox.http('getRoomBillUser', {
										bill_id: billList[0].id
									})({
										method: 'post'
									}).then(res2 => {
										if (res2.data.length > 0) {
											this.authInfo = res2.data.filter(item => {
												return item.common_code == this.userInfo
													.common_code
											})[0]
											this.getRoomBillUser(res2.data)
											if (!this.hotelBill.team_id) {
												// 这里判断是否已认证但未办理成功，bill_status=3,anthour=1
												if (res1.data.bill_status == 3 && this.authInfo
													.authentication == 1 && res1.data.room_number
												) {
													uni.showModal({
														title: '提示',
														content: '您已经通过认证，点击确认办理入住！',
														showCancel: false,
														success: res_sure => {
															if (res_sure.confirm) {
																this.$iBox.http(
																	'selfCheckIn', {
																		bill_id: res1
																			.data
																			.id
																	})({
																	method: 'post'
																}).then(
																	res_self => {
																		uni.showModal({
																			title: '提示',
																			content: '认证成功!',
																			showCancel: false,
																			success: res6 => {
																				if (res6
																					.confirm
																				) {
																					uni.reLaunch({
																						url: '/pages/myRoom/myRoom'
																					})
																				}
																			}
																		})
																	}).catch(
																	err => {
																		if (this
																			.authInfo
																			.is_main
																		) {
																			uni.navigateTo({
																				url: '/packageA/autoRoom/chooseRoom/chooseRoom'
																			})
																		} else {
																			uni.showModal({
																				title: '提示',
																				content: '请等待主入住人支付房费或押金后重试！',
																				showCancel: false,
																				success: res6 => {}
																			})
																		}
																	})
															}
														}
													})


												} else if (this.authInfo.authentication == 1 && !
													res1.data.room_number) {
													// 入住中已经认证但是没有房号
													uni.navigateTo({
														url: '/packageA/autoRoom/chooseRoom/chooseRoom'
													})

												} else if (this.authInfo.authentication == 0 &&
													res1.data.room_number) {
													if (this.userInfo.phone && this.userInfo
														.grade_info && this.userInfo.grade_info
														.upgrade_growth_value > -1) {
														if (res1.data.bill_status == 4) {
															uni.showModal({
																title: '提醒',
																content: '您有入住订单，请先进行认证!',
																success: (res) => {
																	if (res.confirm) {
																		uni.navigateTo({
																			url: '/packageA/autoRoom/autoRoom'
																		})
																	}
																}
															})
														} else {
															// 查询房费
															this.$iBox.http(
																'getSelfCheckInAmount', {
																	bill_id: this.hotelBill.id
																})({
																method: 'post'
															}).then(res => {
																this.cashAll = res.data
																// 查询是否支持在线收押金
																this.online_cash = this
																	.shopSetting.filter(
																		item => {
																			return item
																				.sign ==
																				'self_check_in_pay_cash_pledge'
																		})[0].property
																	.status

																if (!this.hotelBill
																	.team_id) {
																	console.log('测试跳转',
																		this.cashAll
																		.bill_amount +
																		this.cashAll
																		.cash_pledge -
																		this.cashAll
																		.already_pay -
																		this.cashAll
																		.already_pay_cash_pledge >
																		0);
																	if (!this
																		.online_cash) {
																		console.log(
																			'测试跳转2');
																		// 需要在线支付判断是否已经支付了押金和房费，>0则代表需要支付,或者没有选择入住人数在房间人数大于1的情况下
																		if (this.cashAll
																			.bill_amount +
																			this.cashAll
																			.cash_pledge -
																			this.cashAll
																			.already_pay -
																			this.cashAll
																			.already_pay_cash_pledge >
																			0 ||
																			(this.hotelBill
																				.max_user_count >
																				1 && this
																				.hotelBill
																				.select_user_count
																			)) {
																			// uni.navigateTo({
																			// 	url: '/packageA/autoRoom/autoRoom'
																			// })
																		}
																	} else {

																		// 不在线交押金则判断房费是否交清
																		if (this.cashAll
																			.bill_amount -
																			this.cashAll
																			.already_pay >
																			0 ||
																			(this.hotelBill
																				.max_user_count >
																				1 && this
																				.hotelBill
																				.select_user_count
																			)) {
																			// uni.navigateTo({
																			// 	url: '/packageA/autoRoom/autoRoom'
																			// })
																		} else {
																			// uni.navigateTo({
																			// 	url: '/packageA/autoRoom/autoRoom'
																			// })

																		}
																	}
																}
															})
														}
													} else {
														console.log('dsdsdsdbbbbbbbbbbb');
														uni.reLaunch({
															url: '/pages/myRoom/myRoom'
														})

													}
												}
											} else {
												if (res1.data.bill_status == 3 && this.authInfo
													.authentication == 1 && res1.data.room_number
												) {
													uni.showModal({
														title: '提示',
														content: '订单状态为待入住，请联系酒店!'
													})

												} else if (this.authInfo.authentication == 0 &&
													res1.data.room_number) {
													uni.navigateTo({
														url: '/packageB/teamCheckIn/confirmTeam?team_id=' +
															this.hotelBill.team_id
													})
												}
											}

										} else {
											this.authInfo = null
											this.getRoomBillUser([])
										}
										this.billShow = false
										uni.hideLoading()
									})
								})
							} else {
								// 3.如果是多个订单，则循环判断是否有已认证的，有一个则正常显示，若一个都没则弹窗提示多个订单选择一个进行选房

								this.billList = res.data
								// 自动切换到有订单的酒店第一个
								let hotelChoose = this.hotelList.filter(item => {
									return item.id == this.billList[0].shop_id
								})[0]

								if (!hotelChoose) {
									uni.showModal({
										title: '提示',
										content: '此订单酒店已经下架',

										success: res => {
											if (res.confirm) {
												this.getHotel({})
											}
										}
									})
								} else {
									this.getHotel(hotelChoose)
								}


								let shop_id = this.billList[0].shop_id
								this.pageUI(shop_id)
								let detail = null
								let userNum = 0

								for (let item of billList) {
									if (item.users.length > 0) {
										for (let item1 of item.users) {
											if (item1.common_code == this.userInfo.common_code) {

												if (item1.authentication || item1.user_status) {
													this.hotelBill = item
													if (this.hotelBill && this.hotelBill.clean_status != 1) {
														this.ifZang = true
														if (this.hotelBill.dirty_check_in == 0) {
															this.ifZangCurrent = 0
														} else {
															this.ifZangCurrent = 1
														}

													} else {
														this.ifZang = false
													}
													this.getBillDetail(item)
													this.getHardWareList(this.hotelBill.hardware_list)
													this.authInfo = item1
													this.getRoomBillUser(item1)
													uni.hideLoading()
													break
												} else {
													userNum = userNum + 1
												}
											}
										}
									} else {
										userNum = userNum + 1
									}
								}

								if (userNum == billList.length) {
									this.billShow = true
								} else {
									this.billShow = false
								}
							}
						} else {
							console.log('没有登录');
							let shop_id = this.hotel.id
							this.pageUI(shop_id)
							uni.hideLoading()
							this.hackReset = false
							this.$nextTick(() => {
								this.hackReset = true
							})

						}

					}).catch((error) => {
						console.log(error, 'wodecuo');
						uni.showModal({
							title: '提示',
							content: error,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									wx.reLaunch({
										url: '/pages/index/index'
									})
								}
							}
						})
					})

				} else {
					//未开启则直接搜索，判断订单数量，若大于1个，则弹窗选择并展示订单，

					// 首先判断有没有订单,
					// 查询订单
					// uni.showLoading({
					// 	title: '加载中...'
					// })
					this.params.page = 1
					this.$iBox.http('getUserRoomBillList', this.params)({
						method: 'post'
					}).then(res => {
						if (res.data != 'no_login_bill') {
							let billList = res.data
							// .filter(item => {
							// 	return item.shop_id == this.hotel.id
							// })
							if (billList.length == 0) {
								let shop_id = this.hotel.id
								this.hotelBill = ''
								console.log(this.hotelBill, '是否还有订单');
								this.pageUI(shop_id)
								uni.hideLoading()

							} else if (billList.length == 1) { // 2.如果有订单，则判断订单数，如果订单是1个则正常显示判断是否认证

								console.log(this.hotelList, 'll');
								// 自动切换到有订单的酒店
								let hotelChoose = this.hotelList.filter(item => {
									return item.id == billList[0].shop_id
								})[0]
								console.log(hotelChoose, '是否还有订单1');
								if (!hotelChoose) {
									uni.showModal({
										title: '提示',
										content: '此订单酒店已经下架',
										showCancel: false,
										success: res => {
											if (res.confirm) {
												this.getHotel({})
											}
										}
									})
								} else {
									this.getHotel(hotelChoose)
								}


								this.$iBox.http('getRoomBillInfo', {
									bill_id: billList[0].id
								})({
									method: 'post'
								}).then(res1 => {

									let detail = null
									this.hotelBill = res1.data

									if (this.hotelBill && this.hotelBill.team_id) {
										let self = this
										this.timeInfo && clearInterval(this.timeInfo)
										this.getTeamInfo({team_id:this.hotelBill.team_id}); // 立即执行第一次
										this.timeInfo = setInterval(() => {
											this.getTeamInfo({team_id:this.hotelBill.team_id});
										}, 5000);
									}

									if (this.hotelBill && this.hotelBill.clean_status != 1) {
										this.ifZang = true
										if (this.hotelBill.dirty_check_in == 0) {
											this.ifZangCurrent = 0
										} else {
											this.ifZangCurrent = 1
										}

									} else {
										this.ifZang = false
									}
									this.getBillDetail(res1.data)
									let shop_id = this.hotelBill.shop_id
									this.getHardWareList(res1.data.hardware_list)
									this.pageUI(shop_id)
								})


							} else {
								this.hotelBill = ''
								let shop_id = this.hotel.id
								this.pageUI(shop_id)
							}
						}
					}).catch((error) => {
						uni.showModal({
							title: '提示',
							content: error,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									wx.reLaunch({
										url: '/pages/myRoom/myRoom'
									})
								}
							}
						})
					})
				}
			},
			//===========================================权限验证=================================================
			getWxAuthorizeLocation() {
				uni.getSetting({
					success: (res) => {

						// 如果从未申请定位权限，则申请定位权限
						if (res.authSetting['scope.userLocation'] == null) {
							uni.authorize({
								scope: 'scope.userLocation',
								success() {
									// 用户同意
									// 相关操作

								},
								fail() {
									uni.showToast({
										title: '无法申请定位权限,请确认是否已经授权定位权限',
										icon: "none",
										duration: 2000
									})

								}
							})
							return
						}

						// if (res.authSetting['scope.bluetooth'] == null) {
						// 	uni.authorize({
						// 		scope: 'scope.bluetooth',
						// 		success() {
						// 			// 用户同意
						// 			// 相关操作
						// 		},
						// 		fail() {
						// 			uni.showToast({
						// 				title: '无法申请蓝牙权限,请确认是否已经授权蓝牙权限',
						// 				icon: "none",
						// 				duration: 2000
						// 			})
						// 		}
						// 	})
						// 	return
						// }


					}
				});
			},
		},
		onPullDownRefresh() {
			this.params.bill_id = this.bill_id
			this.params.code = this.code
			this.showMt()
			wx.stopPullDownRefresh()
		},
		onShareAppMessage() {
			let url = ''

			if (this.shareSign == 'share_key' || !this.shareSign) {
				// 1.返回节点对象
				let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
				let currentPage = pages[pages.length - 1]; //获取当前页面的对象
				url = currentPage.route //当前页面url
				console.log(url, 'il');
				let type = this.shareSign == 'share_key' ? 2 : 3
				console.log('ti', this.hotelBill.id, 'kl', this.share_code, !this.team_id);
				if (!this.team_id) {
					if (this.hotelBill) {
						return {
							path: url + '?b=' + this.hotelBill.id + '&c=' + this.share_code + '&type=' + type
						};
					} else {
						return {
							path: url
						};
					}


				} else {
					console.log('ti1');
					return {
						path: '/packageB/teamCheckIn/teamCheckIn' + '?b=' + this.hotelBill.id + '&c=' + this.share_code +
							'&t=' + this.team_id + '&name=' + this.authInfo.name + '&gender=' + this.authInfo.gender
					};
				}

			} else if (this.shareSign == 'visitor') {
				url = '/packageA/autoRoom/visitorPage/visitorPage'

				return {

					path: url + '?bill_id=' + this.hotelBill.id + '&code=' + this.share_code
				};
			}
		},
		onUnload() {
			clearInterval(this.timeInfo)
		},
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.noBillBtn {
		height: 96rpx;
		min-width: 622rpx;
		border-radius: 32rpx;
		// margin-left: 40rpx;
		margin-top: 32rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: rgba(0, 0, 0, 0.24) 0px 3rpx 8rpx;
	}

	.searchBoxBtn {
		width: 680rpx;
		// min-height: 300rpx;
		display: flex;

		align-items: center;
		margin-top: 32rpx;

		.search_btn {
			height: 96rpx;
			width: 622rpx;
			border-radius: 32rpx;
			margin: 0 auto;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3rpx 8rpx;
			font-size: 24rpx;
		}
	}

	.BillBox {
		width: 100%;
		margin-top: 30rpx;
		padding: 30rpx;
		height: 70vh;

		.billCard {
			width: 660rpx;
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 10rpx;
			font-weight: 600;
			margin: 20rpx auto;
		}
	}
</style>