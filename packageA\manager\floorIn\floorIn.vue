<template>
	<view class="elevator-container">
		<!-- 标题 -->
		<view class="title">乘坐电梯</view>

		<!-- 模式切换按钮 -->
		<view class="mode-switch">
			<view class="switch-button" @click="toggleQRMode" :class="{ active: !showQRCode }">
				<uni-icons type="home" size="16" style="margin-right: 10rpx;"></uni-icons>
				<text>楼层选择</text>
			</view>
			<view class="switch-button" @click="toggleQRMode" :class="{ active: showQRCode }">
				<text class="icon-erweima" style="margin-right: 10rpx;"></text>
				<text>二维码</text>
			</view>
		</view>

		<!-- 楼层网格模式 -->
		<view class="floor-section" v-if="!showQRCode">
			<view class="floor-tips">
				<text>点击楼层按钮，蓝牙自动连接电梯</text>
			</view>

			<view class="floor-grid">
				<view
					v-for="floor in floors"
					:key="floor"
					class="floor-button"
					:class="{
						normal: getFloorState(floor) === 'normal',
						loading: getFloorState(floor) === 'loading',
						success: getFloorState(floor) === 'success',
						error: getFloorState(floor) === 'error'
					}"
					@click="onFloorClick(floor)"
				>
					<view v-if="getFloorState(floor) === 'loading'" class="loading-icon">
						<uni-icons type="spinner-cycle" size="20" color="#fff"></uni-icons>
					</view>
					<view v-else-if="getFloorState(floor) === 'success'" class="success-icon">
						<uni-icons type="checkmarkempty" size="20" color="#fff"></uni-icons>
					</view>
					<view v-else-if="getFloorState(floor) === 'error'" class="error-icon">
						<text class="icon-close"></text>
					</view>
					<text v-else class="floor-number">{{ floor }}</text>
				</view>
			</view>

			<!-- 蓝牙状态提示 -->
			<view class="bluetooth-status" v-if="bluetoothStatus">
				<text :style="{ color: bluetoothStatus.color }">{{ bluetoothStatus.message }}</text>
			</view>

			<!-- 语音输入区域 -->
			<!-- <view class="voice-section">
				<view class="voice-title">
					<uni-icons type="mic" size="16" color="#2979ff"></uni-icons>
					<text>语音输入</text>
				</view>

				<view class="voice-input-container">
					<view
						class="voice-button"
						:class="{
							recording: isRecording,
							processing: isProcessingVoice
						}"
						@touchstart="startVoiceInput"
						@touchend="stopVoiceInput"
						@touchcancel="cancelVoiceInput"
					>
						<view class="voice-icon" v-if="!isRecording && !isProcessingVoice">
							<uni-icons type="mic-filled" size="30" color="#fff"></uni-icons>
						</view>
						<view class="voice-icon recording-animation" v-else-if="isRecording">
							<uni-icons type="mic-filled" size="30" color="#fff"></uni-icons>
						</view>
						<view class="voice-icon" v-else>
							<uni-icons type="spinner-cycle" size="30" color="#fff"></uni-icons>
						</view>
					</view>

					<view class="voice-tips">
						<text v-if="!isRecording && !isProcessingVoice">长按说出楼层数字</text>
						<text v-else-if="isRecording">正在录音，请说话...</text>
						<text v-else>正在识别语音...</text>
					</view>
				</view> -->

				<!-- 语音识别结果 -->
				<!-- <view class="voice-result" v-if="voiceResult">
					<view class="result-text">
						<text>识别结果：{{ voiceResult.text }}</text>
						<text v-if="voiceResult.floor" class="floor-result">→ {{ voiceResult.floor }}层</text>
					</view>
					<view class="result-actions">
						<view class="action-button confirm" @click="confirmVoiceFloor" v-if="voiceResult.floor">
							<text>确认前往</text>
						</view>
						<view class="action-button retry" @click="clearVoiceResult">
							<text>重新录音</text>
						</view>
					</view>
				</view>
			</view> -->
		</view>

		<!-- 二维码模式 -->
		<view class="qr-section" v-if="showQRCode">
			<view class="qr-container">
				<image :src="url" class="qr-image" mode="aspectFit"></image>
				<view class="qr-tips">
					<text style="font-size: 28rpx;color: #CD1225;">*若二维码没生效请点击下方刷新按钮</text>
					<view class="refresh-button" @click="reload">
						<uni-icons type="refresh" size="23" color="#2979ff"></uni-icons>
						<text style="font-size: 34rpx;color: #2979ff;">刷新</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import qrcode1 from '@/packageA/plugins/qrcode.js';
	import QR from '@/packageA/plugins/wxqrcode.js';
	export default {
		data() {
			return {
				floorList: [],
				url: '',
				LiftConf: [],
				// 新增数据
				floors: [], // 动态从接口获取的可用楼层
				floorStates: {}, // 楼层状态管理
				showQRCode: false, // 是否显示二维码模式
				bluetoothStatus: null, // 蓝牙状态提示
				// 蓝牙相关
				bleDeviceId: null,
				serviceId: null,
				characteristicId: null,
				isConnecting: false,
				connectionTimeout: null,
				currentFloor: null, // 当前操作的楼层
				// 语音输入相关
				isRecording: false, // 是否正在录音
				isProcessingVoice: false, // 是否正在处理语音
				voiceResult: null, // 语音识别结果 {text: '', floor: null}
				recordManager: null, // 录音管理器
				voiceTimeout: null // 语音处理超时
			};
		},
		computed: {
			...mapState('login', ['userInfo', 'theme', 'menu']),
			...mapState('hotel', ['hotel']),
		},
		onLoad() {
			this.loadFloorConfig();
		},

		onUnload() {
			// 页面销毁时清理蓝牙连接
			this.closeConnection();
			this.resetFloorStates();
			// 清理语音相关资源
			this.cleanupVoice();
		},
		methods: {
			// 加载楼层配置
			loadFloorConfig() {
				this.$iBox
					.http('getLiftConfigList', {shop_id: this.hotel.id})({
						method: 'post'
					})
					.then(res => {
						if (res.data && res.data.length > 0) {
							this.LiftConf = res.data;
							// 更新可用楼层列表
							this.updateAvailableFloors();
						} else {
							uni.showToast({
								title: '获取电梯配置失败',
								icon: 'none'
							});
						}
					})
					.catch(err => {
						console.error('获取电梯配置失败:', err);
						uni.showToast({
							title: '获取电梯配置失败',
							icon: 'none'
						});
					});
			},

			// 更新可用楼层列表
			updateAvailableFloors() {
				const availableFloors = this.getAvailableFloors();
				// 转换为数字并排序
				this.floors = availableFloors
					.map(floor => parseInt(floor))
					.filter(floor => !isNaN(floor))
					.sort((a, b) => a - b);

				console.log('可用楼层:', this.floors);
			},

			// 模式切换
			toggleQRMode() {
				this.showQRCode = !this.showQRCode;
				this.resetFloorStates();
				this.bluetoothStatus = null;

				// 如果切换到二维码模式，生成二维码
				if (this.showQRCode) {
					this.getqr();
				} else {
					// 切换回楼层模式时，重新加载楼层配置
					this.loadFloorConfig();
				}
			},

			// 楼层点击处理
			onFloorClick(floor) {
				// 检查是否有其他楼层正在连接
				if (this.isConnecting) {
					uni.showToast({
						title: '请等待当前操作完成',
						icon: 'none'
					});
					return;
				}

				this.currentFloor = floor;
				this.updateFloorState(floor, 'loading');
				this.bluetoothStatus = { message: '获取电梯配置...', color: '#2979ff' };

				// 实时获取电梯配置
				this.getLiftConfigAndConnect(floor);
			},

			// 获取楼层状态
			getFloorState(floor) {
				return this.floorStates[floor] || 'normal';
			},



			// 更新楼层状态
			updateFloorState(floor, state) {
				this.$set(this.floorStates, floor, state);

				// 成功或失败状态2秒后自动重置
				if (state === 'success' || state === 'error') {
					setTimeout(() => {
						this.$set(this.floorStates, floor, 'normal');
					}, 2000);
				}
			},

			// 重置所有楼层状态
			resetFloorStates() {
				this.floorStates = {};
				this.isConnecting = false;
				this.currentFloor = null;
			},

			// 获取电梯配置并连接
			getLiftConfigAndConnect(floor) {
				this.$iBox
					.http('getLiftConfigList', { shop_id: this.hotel.id })({
						method: 'post'
					})
					.then(res => {
						if (res.data && res.data.length > 0) {
							// 更新电梯配置
							this.LiftConf = res.data;

							// 检查楼层是否在配置范围内
							const availableFloors = this.getAvailableFloors();
							if (!availableFloors.includes(floor.toString())) {
								this.handleBluetoothError(`${floor}层暂不可用`);
								return;
							}

							this.bluetoothStatus = { message: '正在连接电梯...', color: '#2979ff' };
							// 开始蓝牙连接
							this.initBluetoothAndConnect(floor);
						} else {
							this.handleBluetoothError('获取电梯配置失败');
						}
					})
					.catch(err => {
						console.error('获取电梯配置失败:', err);
						this.handleBluetoothError('获取电梯配置失败');
					});
			},

			// 获取可用楼层列表
			getAvailableFloors() {
				const floors = new Set();
				this.LiftConf.forEach(lift => {
					if (lift.floor_config && Array.isArray(lift.floor_config)) {
						lift.floor_config.forEach(floor => floors.add(floor));
					}
				});
				return Array.from(floors);
			},

			// 初始化蓝牙并连接
			initBluetoothAndConnect(floor) {
				wx.openBluetoothAdapter({
					mode: 'central',
					success: e => {
						console.log('初始化蓝牙成功:' + e.errMsg);
						this.connectAndSendData(floor);
					},
					fail: e => {
						console.log('初始化蓝牙失败，错误码：' + (e.errCode || e.errMsg));
						this.handleBluetoothError('请检查蓝牙是否打开');
					}
				});
			},

			// 连接并发送数据
			connectAndSendData(floor) {
				this.isConnecting = true;

				// 开始扫描附近的蓝牙设备
				wx.startBluetoothDevicesDiscovery({
					allowDuplicatesKey: false,
					success: (res) => {
						console.log('开始扫描蓝牙设备:', res);

						// 设置扫描超时
						this.connectionTimeout = setTimeout(() => {
							this.stopScan();
							this.handleBluetoothError('未找到电梯蓝牙设备');
						}, 10000);

						// 监听发现的设备
						this.onBluetoothDeviceFound(floor);
					},
					fail: (err) => {
						console.error('扫描蓝牙设备失败:', err);
						this.handleBluetoothError('蓝牙扫描失败');
					}
				});
			},

			// 发现蓝牙设备时的处理
			onBluetoothDeviceFound(floor) {
				wx.onBluetoothDeviceFound((res) => {
					const devices = res.devices;
					console.log('发现设备:', devices);

					// 查找电梯设备（根据设备名称）
					const targetDevice = devices.find(device => {
						return device.name && (
							device.localName && (device.localName.includes('SMARTBOX_E'))
						);
					});

					if (targetDevice) {
						console.log('发现电梯设备:', targetDevice);
						this.stopScan();
						this.connectToDevice(targetDevice.deviceId, floor);
					}
				});
			},

			// 停止扫描
			stopScan() {
				wx.stopBluetoothDevicesDiscovery({
					success: (res) => {
						console.log('停止扫描蓝牙设备:', res);
					}
				});

				if (this.connectionTimeout) {
					clearTimeout(this.connectionTimeout);
					this.connectionTimeout = null;
				}
			},

			reload(){
				this.getqr()
			},
			// 连接到设备
			connectToDevice(deviceId, floor) {
				this.bluetoothStatus = { message: '正在连接设备...', color: '#2979ff' };

				wx.createBLEConnection({
					deviceId: deviceId,
					success: (res) => {
						console.log('连接蓝牙设备成功:', res);
						this.bleDeviceId = deviceId;
						this.bluetoothStatus = { message: '获取服务中...', color: '#2979ff' };

						// 获取服务
						this.getServices(deviceId, floor);
					},
					fail: (err) => {
						console.error('连接蓝牙设备失败:', err);
						this.handleBluetoothError('连接电梯失败');
					}
				});
			},

			// 获取蓝牙服务
			getServices(deviceId, floor) {
				wx.getBLEDeviceServices({
					deviceId: deviceId,
					success: (res) => {
						console.log('获取服务成功:', res.services);

						for (let item of res.services) {
							wx.getBLEDeviceCharacteristics({
								deviceId: deviceId,
								serviceId: item.uuid,
								success: (res) => {
									console.log('获取特征值成功:', res.characteristics);
									res.characteristics.forEach(char => {
										if (char.properties.write && !char.properties.read) {
											const writeCharacteristic = char;
											console.log('找到写特征值:', writeCharacteristic);
											if (writeCharacteristic) {
												this.characteristicId = writeCharacteristic.uuid;
												this.sendElevatorCommand(deviceId, item.uuid, writeCharacteristic.uuid, floor);
											}
										}
									});
								},
								fail: (err) => {
									console.error('获取特征值失败:', err);
									this.handleBluetoothError('获取设备特征失败');
								}
							});
						}
					},
					fail: (err) => {
						console.error('获取服务失败:', err);
						this.handleBluetoothError('获取设备服务失败');
					}
				});
			},

			// 发送电梯控制命令
			sendElevatorCommand(deviceId, serviceId, characteristicId, floor) {
				this.bluetoothStatus = { message: '发送控制命令...', color: '#2979ff' };

				// 生成电梯控制数据
				const command = this.generateElevatorData(floor);
				console.log('发送命令:', command, deviceId, serviceId, characteristicId);

				wx.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: this.hexStringToArrayBuffer(command),
					success: (res) => {
						console.log('发送电梯命令成功:', res);
						this.handleBluetoothSuccess();
					},
					fail: (err) => {
						console.error('发送电梯命令失败:', err);
						this.handleBluetoothError('电梯启动失败');
					}
				});
			},

			getqr() {
				this.timestamp = 300
				let a = []
				let floorList = []
				if(this.floorList.length > 0){
					this.floorList.forEach(item1 => {
						floorList.push(item1)
					})
				}

				floorList = [...new Set(floorList)]

				floorList.forEach(item => {
					let floor = {
						floor: item
					}
					a.push(floor)

				})
				console.log(a,'dd');
				let floors = []

				this.LiftConf.forEach(item => {
					let floor = {}
					floor.sn = item.sn
					floor.floors = a
					floors.push(floor)
				})


				let lifts = {
					floors: floors,
					all_lift:1,
					direct_arrival: 0
				}


				console.log(lifts);
				let url = qrcode1.generateAccessCode('a', new Date(), 30, Number(this.userInfo.id), lifts, [])
				console.log(url);
				this.url = QR.createQrCodeImg(url.encrypt)
			},

			// 生成电梯控制数据
			generateElevatorData(floor) {
				// 梯控协议格式：
				// 起始码(1) + 数据长度(1) + 功能码(1) + 地址码1(1) + 地址码2(1) + 数据(N) + 校验码(2)
				// 示例：59 07 00 ff 16 03 66 02 (3楼)

				const frame = new Uint8Array(8);

				// 1. 起始码
				frame[0] = 0x59;

				// 2. 数据长度
				frame[1] = 0x07;

				// 3. 功能码
				frame[2] = 0x00;

				// 4. 地址码1
				frame[3] = 0xff;

				// 5. 地址码2
				frame[4] = 0x16;

				// 6. 数据 (楼层号)
				frame[5] = parseInt(floor.toString(16), 16);

				// 7. 校验码计算
				let checksum = 0xEE;
				for (let i = 0; i < 6; i++) {
					checksum += frame[i];
				}

				// 校验码取最后两个字节 (低字节在前，高字节在后)
				frame[6] = checksum & 0xFF;
				frame[7] = (checksum >> 8) & 0xFF;

				const deviceName = Array.from(frame)
					.map(b => b.toString(16).padStart(2, '0').toUpperCase())
					.join('');
				return deviceName;
			},

			// 十六进制字符串转ArrayBuffer
			hexStringToArrayBuffer(hexString) {
				const cleanHex = hexString.replace(/[^0-9a-fA-F]/g, '');
				if (cleanHex.length % 2 !== 0) {
					throw new Error("Hex string length must be even.");
				}
				const buffer = new Uint8Array(cleanHex.length / 2);
				for (let i = 0; i < cleanHex.length; i += 2) {
					buffer[i / 2] = parseInt(cleanHex.substr(i, 2), 16);
				}
				return buffer.buffer;
			},

			// 处理蓝牙成功
			handleBluetoothSuccess() {
				this.isConnecting = false;
				this.bluetoothStatus = { message: '电梯已启动！', color: '#4CAF50' };

				if (this.currentFloor) {
					this.updateFloorState(this.currentFloor, 'success');
				}

				// 显示成功提示
				uni.showToast({
					title: '电梯已启动',
					icon: 'success'
				});

				// 延迟关闭连接
				setTimeout(() => {
					this.closeConnection();
					this.bluetoothStatus = null;
				}, 2000);
			},

			// 处理蓝牙错误
			handleBluetoothError(message) {
				this.isConnecting = false;
				this.bluetoothStatus = { message: message, color: '#F44336' };

				if (this.currentFloor) {
					this.updateFloorState(this.currentFloor, 'error');
				}

				// 显示错误提示
				uni.showToast({
					title: message,
					icon: 'none'
				});

				// 清理连接
				this.closeConnection();

				// 延迟清除状态
				setTimeout(() => {
					this.bluetoothStatus = null;
				}, 3000);
			},

			// 关闭蓝牙连接
			closeConnection() {
				if (this.bleDeviceId) {
					wx.closeBLEConnection({
						deviceId: this.bleDeviceId,
						success: (res) => {
							console.log('关闭蓝牙连接成功:', res);
						}
					});
					this.bleDeviceId = null;
				}

				wx.closeBluetoothAdapter({
					success: (res) => {
						console.log('关闭蓝牙模块成功:', res);
					}
				});

				this.stopScan();
			},
	}
}
</script>

<style lang="scss" scoped>
.elevator-container {
	padding: 40rpx;
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

	.title {
		text-align: center;
		font-size: 54rpx;
		font-weight: 600;
		margin-bottom: 40rpx;
		color: #333;
	}

	.mode-switch {
		display: flex;
		justify-content: center;
		margin-bottom: 40rpx;
		background: #fff;
		border-radius: 50rpx;
		padding: 8rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

		.switch-button {
			flex: 1;
			height: 70rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 42rpx;
			font-size: 28rpx;
			transition: all 0.3s ease;
			color: #666;

			&.active {
				background: #2979ff;
				color: #fff;
				box-shadow: 0 2rpx 8rpx rgba(41, 121, 255, 0.3);
			}
		}
	}

	.floor-section {
		.floor-tips {
			text-align: center;
			margin-bottom: 30rpx;
			padding: 20rpx;
			background: rgba(255, 255, 255, 0.9);
			border-radius: 20rpx;

			text {
				font-size: 28rpx;
				color: #666;
			}
		}

		.floor-grid {
			display: grid;
			grid-template-columns: repeat(6, 1fr);
			gap: 20rpx;
			margin-bottom: 40rpx;

			.floor-button {
				height: 80rpx;
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 32rpx;
				font-weight: 600;
				transition: all 0.3s ease;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
				position: relative;
				overflow: hidden;

				&.normal {
					background: #2979ff;
					color: white;

					&:active {
						transform: scale(0.95);
						box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
					}
				}

				&.loading {
					background: #ccc;
					color: white;

					.loading-icon {
						animation: rotate 1s linear infinite;
					}
				}

				&.success {
					background: #4CAF50;
					color: white;
					animation: pulse 0.6s ease-in-out;
				}

				&.error {
					background: #F44336;
					color: white;
					animation: shake 0.6s ease-in-out;
				}

				.floor-number {
					font-size: 32rpx;
					font-weight: 600;
				}

				.loading-icon, .success-icon, .error-icon {
					font-size: 36rpx;
				}
			}
		}

		.bluetooth-status {
			text-align: center;
			padding: 20rpx;
			background: rgba(255, 255, 255, 0.9);
			border-radius: 20rpx;
			margin-bottom: 20rpx;

			text {
				font-size: 28rpx;
				font-weight: 500;
			}
		}
	}

	.qr-section {
		display: flex;
		flex-direction: column;
		align-items: center;

		.qr-container {
			background: #fff;
			border-radius: 30rpx;
			padding: 40rpx;
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

			.qr-image {
				height: 440rpx;
				width: 440rpx;
				margin-bottom: 30rpx;
			}

			.qr-tips {
				display: flex;
				flex-direction: column;
				align-items: center;

				.refresh-button {
					margin-top: 30rpx;
					display: flex;
					align-items: center;
					padding: 15rpx 30rpx;
					background: #f0f8ff;
					border-radius: 25rpx;

					text {
						margin-left: 10rpx;
					}
				}
			}
		}
	}

	// 语音输入区域样式
	.voice-section {
		margin-top: 40rpx;
		padding: 30rpx;
		background: rgba(255, 255, 255, 0.95);
		border-radius: 25rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

		.voice-title {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 30rpx;

			text {
				margin-left: 10rpx;
				font-size: 32rpx;
				font-weight: 600;
				color: #2979ff;
			}
		}

		.voice-input-container {
			display: flex;
			flex-direction: column;
			align-items: center;

			.voice-button {
				width: 120rpx;
				height: 120rpx;
				border-radius: 60rpx;
				background: #2979ff;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 20rpx;
				transition: all 0.3s ease;
				box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.3);

				&:active {
					transform: scale(0.95);
				}

				&.recording {
					background: #F44336;
					animation: pulse 1s infinite;

					.recording-animation {
						animation: bounce 0.6s infinite alternate;
					}
				}

				&.processing {
					background: #FF9800;

					.voice-icon {
						animation: rotate 1s linear infinite;
					}
				}

				.voice-icon {
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.voice-tips {
				text-align: center;

				text {
					font-size: 28rpx;
					color: #666;
				}
			}
		}

		.voice-result {
			margin-top: 30rpx;
			padding: 25rpx;
			background: #f8f9fa;
			border-radius: 15rpx;
			border-left: 4rpx solid #2979ff;

			.result-text {
				margin-bottom: 20rpx;

				text {
					font-size: 28rpx;
					color: #333;

					&.floor-result {
						color: #2979ff;
						font-weight: 600;
						margin-left: 10rpx;
					}
				}
			}

			.result-actions {
				display: flex;
				gap: 20rpx;

				.action-button {
					flex: 1;
					height: 70rpx;
					border-radius: 35rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						font-size: 28rpx;
						font-weight: 500;
					}

					&.confirm {
						background: #4CAF50;
						color: white;
					}

					&.retry {
						background: #f0f0f0;
						color: #666;
					}
				}
			}
		}
	}
}

// 动画效果
@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

@keyframes pulse {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.1);
	}
	100% {
		transform: scale(1);
	}
}

@keyframes shake {
	0%, 100% {
		transform: translateX(0);
	}
	25% {
		transform: translateX(-5rpx);
	}
	75% {
		transform: translateX(5rpx);
	}
}

@keyframes bounce {
	0% {
		transform: translateY(0);
	}
	100% {
		transform: translateY(-10rpx);
	}
}
</style>
