/**
 * 管理员页面主题混入
 * 确保管理员页面也能正确应用主题配置
 */
import globalUIManager from '@/utils/GlobalUIManager'

export default {
	data() {
		return {
			themeApplied: false
		}
	},
	
	async onLoad() {
		// 页面加载时应用主题
		await this.applyAdminTheme()
	},
	
	onShow() {
		// 页面显示时检查主题
		if (!this.themeApplied) {
			this.applyAdminTheme()
		}
	},
	
	methods: {
		// 应用管理员页面主题
		async applyAdminTheme() {
			try {
				console.log('开始应用管理员页面主题...')
				
				// 首先尝试从全局UI管理器获取配置
				let uiData = null
				
				// 检查是否有缓存的UI配置
				if (globalUIManager.getStatus().hasData) {
					uiData = globalUIManager.uiData
					console.log('使用缓存的UI配置应用主题')
				} else {
					// 如果没有缓存，重新获取
					console.log('重新获取UI配置...')
					uiData = await globalUIManager.getUIConfig()
				}
				
				if (uiData) {
					// 应用主题配置
					this.applyThemeToAdminPage(uiData)
					this.themeApplied = true
					console.log('管理员页面主题应用成功')
				} else {
					console.warn('无法获取UI配置，使用默认主题')
					this.applyDefaultTheme()
				}
				
			} catch (error) {
				console.error('应用管理员页面主题失败:', error)
				this.applyDefaultTheme()
			}
		},
		
		// 应用主题到管理员页面
		applyThemeToAdminPage(uiData) {
			try {
				// 应用主题色
				if (uiData.subject && uiData.subject.style) {
					this.setAdminThemeColors(uiData.subject.style)
				}
				
				// 应用其他UI配置
				if (uiData.control_component) {
					this.processAdminControlComponents(uiData.control_component)
				}
				
			} catch (error) {
				console.error('应用主题到管理员页面失败:', error)
			}
		},
		
		// 设置管理员页面主题色
		setAdminThemeColors(themeStyle) {
			try {
				// 通过CSS变量设置主题色
				if (typeof document !== 'undefined') {
					const root = document.documentElement
					if (themeStyle.primaryColor) {
						root.style.setProperty('--admin-primary-color', themeStyle.primaryColor)
					}
					if (themeStyle.secondaryColor) {
						root.style.setProperty('--admin-secondary-color', themeStyle.secondaryColor)
					}
				}
				
				// 同时更新store中的主题配置
				if (this.$store) {
					this.$store.dispatch('ui/toThemeColor', themeStyle)
				}
				
				console.log('管理员页面主题色设置成功:', themeStyle)
			} catch (error) {
				console.error('设置管理员页面主题色失败:', error)
			}
		},
		
		// 处理管理员页面控制组件
		processAdminControlComponents(components) {
			try {
				components.forEach(item => {
					// 管理员页面可能需要特殊的组件处理
					if (item.sign === 'admin_theme') {
						// 处理管理员专用主题配置
						this.handleAdminThemeComponent(item)
					}
				})
			} catch (error) {
				console.error('处理管理员控制组件失败:', error)
			}
		},
		
		// 处理管理员主题组件
		handleAdminThemeComponent(component) {
			// 这里可以添加管理员页面特有的主题处理逻辑
			console.log('处理管理员主题组件:', component)
		},
		
		// 应用默认主题
		applyDefaultTheme() {
			console.log('应用默认管理员主题')
			
			const defaultTheme = {
				primaryColor: '#1890ff',
				secondaryColor: '#f0f0f0'
			}
			
			this.setAdminThemeColors(defaultTheme)
			this.themeApplied = true
		},
		
		// 强制刷新主题
		async refreshAdminTheme() {
			console.log('强制刷新管理员页面主题')
			this.themeApplied = false
			
			// 强制重新获取UI配置
			await globalUIManager.refresh()
			await this.applyAdminTheme()
		},
		
		// 检查主题是否已应用
		isThemeApplied() {
			return this.themeApplied
		}
	}
}
