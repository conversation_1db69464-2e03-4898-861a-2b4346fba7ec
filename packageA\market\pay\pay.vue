<template>
	<view class="container">
		<view class="container_heard">
			<view style="font-size: 28rpx;font-weight: 600;margin: 20rpx;" class="">提示:</view>
			<view class="" style="color: indianred;">如遇到问题可以联系酒店前台</view>
		</view>
		<view class="bg-white pt-30 mb-20">
			<view style="font-size: 28rpx;font-weight: 600;padding: 30rpx;background-color: #FFFFFF;" class="">商品列表
			</view>
			<list-cell arrow line-right>
				<view style="display: flex;align-items: center;overflow:hidden;width: 100%;" class="">
					<scroll-view style="overflow:hidden;flex: 1;" scroll-x>
						<view style="width: 100%;display: flex;align-items: center;" class="">
							<view class=""
								style="display: flex;flex-direction: column;justify-content: center;align-items: center;"
								v-for="(item, index) in cart" :key="index">
								<image :src="item.image" class="pro-img"></image>
								<text>x{{item.number}}</text>
							</view>

						</view>
					</scroll-view>
					<view style="flex-shrink: 0;margin-left: 20rpx;" class="">共{{ cartNum }}件</view>
				</view>
			</list-cell>
			<list-cell arrow last>
				<view style="width: 100%;display: flex;align-items: center;justify-content: space-between;" class=""
					@click="chooseCoupon">
					<view style="display: flex;align-items: center;" class="">
						<view>优惠券</view>
						<view class="coupon-label">劵</view>
					</view>
					<view class="" style="display: flex;align-items: center;">
						<text
							style="color: red;font-size: 28rpx;">{{coupon_price==0?(coupNum>0?coupNum +'张优惠券可用':'无可用优惠券'):'已优惠'+coupon_price+'元'}}</text>
					</view>
				</view>
			</list-cell>
			<list-cell>
				<view style="width: 100%;display: flex;align-items: center;justify-content: space-between;" class="">
					<view style="flex-shrink:0;" class="">房间号</view>
					<view class="textarea-box"
						style="width: 200rpx;display: flex;flex-direction: column;align-items: flex-end;">
						<uni-easyinput v-model="roomNumber" placeholder="请输入房间号"></uni-easyinput>
					</view>
				</view>
			</list-cell>
			<list-cell>
				<view style="width: 100%;display: flex;justify-content: space-between;" class="">
					<view style="flex-shrink:0;" class="">备注</view>
					<view class="textarea-box"
						style="margin-top: 10rpx;display: flex;flex-direction:column;align-items: flex-end;width:500rpx;">
						<uni-easyinput type="textarea" v-model="remark" placeholder="请输入内容"></uni-easyinput>
						<view class="tips">{{ remark.length }} / 150</view>
					</view>
				</view>
			</list-cell>
			<list-cell last>
				<view class="" style="display: flex;flex-direction: column;align-items: flex-end;width: 100%;">
					<view style="display: flex;width: 100%;justify-content: flex-end;align-items: center;" class="">
						<text style="font-size: 24rpx;" class="">共{{ cartNum }}件商品，小计</text>
						<text style="font-size: 36rpx;" class="">￥{{ cartAmount }}</text>

					</view>
					<p style="color: brown;">优惠：￥{{coupon_price}}</p>
					<p style="font-weight: 600;">总计: ￥{{lastPrice}}</p>
				</view>

			</list-cell>
		</view>

		<view class="footer">
			<view class="" style="padding: 20rpx;">
				合计：<text style="font-size: 40rpx;font-weight: 600;" class="">￥{{ lastPrice }}</text>
			</view>
			<view class="right" :style="{background:themeColor.main_color}" @click="toPayBox">支付</view>
		</view>

		<!-- 支付弹窗 -->
		<m-popup :show="pop" @closePop="closePop">
			<m-payCard @toPay="payFor" :payType="payList"></m-payCard>
		</m-popup>

		<!-- 选择优惠券弹窗 -->
		<m-popup :show="popCoupon" @closePop="closePopCoupon">
			<m-chooseCoupon :coupType="2" :limit="{limitNum:lastPrice}" @getCouponIfo="getInfo"
				:goods='cart'></m-chooseCoupon>
		</m-popup>

		<!-- 会员支付方式弹出窗 -->
		<m-popup :show="pop1" @closePop="closePop1" mode="center">
			<!-- 无押金 -->
			<view class="customPay">

				<view style="position: absolute;top: 20rpx;right: 30rpx;" @click="closePop1">
					<view class="icon-close" style="font-size: 40rpx;"></view>
				</view>
				<view class=""
					style="width: 90%;height: 200rpx;border-bottom: 1px solid #e4e7ed;display: flex;flex-direction: column;align-items: center;justify-content: space-around;">
					<text style="font-size:30rpx;font-weight: 600;">{{hotel.shop_name}}</text>
					<text style="font-size:40rpx;font-weight: 600;">总计:￥{{lastPrice}}</text>
				</view>
				<view class=""
					style="width: 94%;height: 150rpx;display: flex;align-items: center;justify-content: space-between;">
					<text style="color:909399;font-weight: 600;">￥{{lastPrice}}</text>
					<view class="" style="display: flex;align-items: center;">
						<u-icon name="integral-fill" color="#0055ff"></u-icon>
						<text style="font-weight: 600;">{{payType==1?'跨店会员自动扣除':'单店会员自动扣除'}}</text>
					</view>

				</view>
				<!-- 	<view class="" v-if="coupon_price > 0">
					<text>优惠券:-￥{{coupon_price}}</text>
				</view> -->
				<view class="btn_pay">
					<view class="btn" :style="'background:'+themeColor.main_color+';color:'+themeColor.bg_color"
						@click="surePay">
						<text>确认支付</text>
					</view>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import listCell from '@/components/m-list-cell/m-list-cell.vue'
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		components: {
			listCell
		},
		data() {
			return {
				roomNumber: '',
				remark: '',
				coupNum: 0,
				coupon_price: 0,
				coupon_id: '',
				popCoupon: false,
				coupType: 2,
				pop: false,
				pop1: false,
				payType: 0,
				payList: ['weixin', 'tongyong', 'duli'], //支付方式,
				// idList:[],//购物车物品ID集合
				limitSign: null,
				cart: []
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel']),
			...mapState('market', ['cartList']),
			cartNum() {
				return this.cart.reduce((acc, cur) => acc + cur.number, 0)
			},
			cartAmount() {
				return this.cart.reduce((acc, cur) => acc + cur.number * cur.price, 0)
			},
			lastPrice() {
				return (this.cartAmount - this.coupon_price).toFixed(2)
			}
		},
		onLoad() {
			uni.showLoading({
				title: '加载中...'
			})

			// 查询订单自动填房号
			this.$iBox.http('getRoomBillList', {
				bill_status: 4,
				page: 1,
				limit: 10
			})({
				method: 'post'
			}).then(res => {
				if (res.data.list.length > 0) {
					this.roomNumber = res.data.list[0].room_number
				}
			})
			console.log(this.cartList, 'hhhk');
			this.cart = this.cartList
			let goods_list = []
			this.cart.forEach(item => {
				let item1 = {
					goods_id: '',
					count: 0
				}
				item1.goods_id = item.id
				item1.count = item.number
				goods_list.push(item1)
			})
			this.$iBox.http('getUserMarketCoupon', {
				type_id: this.coupType,
				use_status: 0,
				goods_list: goods_list
			})({
				method: 'post'
			}).then(res => {

				let a = 0
				let coupons = res.data
				coupons.forEach(item => {

					if (item.coupon_info.type_id && Number(item.coupon_info.use_condition) <= this
						.cartAmount && this.$moment().unix() <= item.limit_time && (item.usable_week
							.length > 0 && item.usable_week.includes(this.$moment().isoWeekday()
							.toString()) || item.usable_week.length == 0) && item.usable) {
						item.usable = 1
					} else {
						item.usable = 0
					}
				})

				let list_new = []
				coupons.forEach(item => {
					if ((item.usable)) {
						a += 1
						list_new.push(item)
					}
				})
				this.coupNum = a
				this.coupons = list_new
				console.log(this.coupons, 'dd');
				uni.hideLoading()
			})
		},
		watch: {

			choosePrice: {
				handler(oldData, newData) {
					console.log(this.choosePrice, 'kkkkk');
					this.choosePrice = this.choosePrice
				},
				immediate: true
			}
		},
		methods: {
			chooseCoupon(e) {
				console.log(e, 'eee');
				this.popCoupon = true
			},
			closePopCoupon() {
				this.popCoupon = false
			},
			closePop() {
				this.pop = false
			},
			closePop1() {
				this.pop1 = false
			},
			toPayBox() {
				this.pop = true
			},
			getInfo(e) {
				console.log(e, '选择');
				this.popCoupon = false
				if (e) {

					this.coupon_price = e.coupon_info.discounts
					this.coupon_id = e.id

				} else {
					this.coupon_price = 0
					this.coupon_id = ''
				}

				console.log(this.coupon_price, 'enfi');
			},
			payFor(e) {
				this.$iBox.throttle(() => {
					this.payType = e
					this.fnPay(e)
				}, 2000);
			},
			fnPay(e) {
				console.log(this.planTimeList, this.payType, 'this.planTimeList');
				let goods_list = []
				this.cart.forEach(item => {
					let item1 = {
						goods_id: '',
						count: 0
					}
					item1.goods_id = item.id
					item1.count = item.number
					goods_list.push(item1)
				})

				// 首先判断是否用微信还是余额,0:微信  1:通用余额  2.独立余额
				if (this.payType == 'weixin') {
					// 判断商品是否支持微信支付。现金支付
					for (let item of this.cart) {
						if (!item.pay_type.includes("1")) {
							uni.showToast({
								icon: 'none',
								title: `${item.name}不支持现金支付!`,
								duration: 2000
							})
							return
						}


					}

					uni.showLoading({
						title: '等待支付...'
					})
					let params = {
						shop_id: this.hotel.id,
						goods_list: goods_list,
						booking_type: 1,
						pay_type: 1,
						room_number: this.roomNumber,
						memo: this.remark,
						coupon_id: this.coupon_id
					}

					this.$iBox.http('addStoreGoodsOrder', params)({
						method: 'post'
					}).then(res => {
					if(this.lastPrice==0){
						
						uni.navigateTo({
							url: '/pages/resultsPage/resultsMarketPage',
						
						})
							return;
					}
						if (res.data.bizCode == '0000') {
							// 随行付
							uni.requestPayment({
								provider: 'wxpay',
								AppId: res.data.payAppId,
								timeStamp: res.data.payTimeStamp,
								nonceStr: res.data.paynonceStr,
								package: res.data.payPackage,
								signType: res.data.paySignType,
								paySign: res.data.paySign,
								success: (res) => {
									uni.hideLoading()
									uni.navigateTo({
										url: '/pages/resultsPage/resultsMarketPage'
									})

								},
								fail: function(err) {
									uni.hideLoading()
								}
							});


						} else {
							// 微信支付
							uni.requestPayment({
								provider: 'wxpay',
								timeStamp: res.data.timeStamp,
								nonceStr: res.data.nonceStr,
								package: res.data.package,
								signType: 'MD5',
								paySign: res.data.paySign,
								success: (res) => {
									uni.hideLoading()
									uni.navigateTo({
										url: '/pages/resultsPage/resultsMarketPage'
									})
								},
								fail: function(err) {
									uni.hideLoading()
								}
							});
						}

					})

				} else {
					// 判断商品是否支持微信支付。现金支付
					if (this.payType == 'tongyong') {
						for (let item of this.cart) {
							if (!item.pay_type.includes("2")) {
								uni.showToast({
									icon: 'none',
									title: `${item.name}不支持通用会员支付!`,
									duration: 2000
								})
								return
							}
						}
					} else {
						for (let item of this.cart) {
							if (!item.pay_type.includes("3")) {
								uni.showToast({
									icon: 'none',
									title: `${item.name}不支持独立会员支付!`,
									duration: 2000
								})
								return
							}
						}
					}


					this.pop = false
					this.pop1 = true

				}

			},
			surePay() {
				this.$iBox.throttle1(() => {
					this.customPay()
				}, 2000);
			},
			customPay() {
				console.log(this.payType, 'pay');
				// for (let item of this.cart) {
				// 	if(!item.pay_type.includes("1")){
				// 		uni.showToast({
				// 			icon:'error',
				// 			title:`${item.name}不支持现金支付!`,
				// 			duration:1200
				// 		})
				// 		return
				// 	}
				// }

				uni.showLoading({
					title: '等待支付...',
					mask: true
				})
				let goods_list = []
				this.cart.forEach(item => {
					let item1 = {
						goods_id: '',
						count: 0
					}
					item1.goods_id = item.id
					item1.count = item.number
					goods_list.push(item1)
				})
				let params = {
					shop_id: this.hotel.id,
					goods_list: goods_list,
					booking_type: this.payType != 'daodian' ? 1 : 2,
					pay_type: this.payType == 'tongyong' ? 2 : (this.payType == 'duli' ? 3 : ''),
					room_number: this.roomNumber,
					memo: this.remark,
					coupon_id: this.coupon_id
				}

				this.$iBox.http('addStoreGoodsOrder', params)({
					method: 'post'
				}).then(res => {

					uni.hideLoading()
					uni.navigateTo({
						url: '/pages/resultsPage/resultsMarketPage'
					})


				})


			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: auto;
		padding-bottom: 120rpx;

		.container_heard {
			background-color: #FFFFFF;
			padding: 30rpx;
			display: flex;
			align-items: center;
		}
	}

	.pro-img {
		width: 120rpx;
		height: 90rpx;
		flex-shrink: 0;
	}

	.coupon-label {
		background-color: $color-primary;
		color: #FFFFFF;
		font-size: 18rpx;
		line-height: 0.9rem;
		width: 0.9rem;
		height: 0.9rem;
		margin-left: 10rpx;
		text-align: center;
	}

	.wx-pay-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}

	.footer {
		background-color: #FFFFFF;
		z-index: 10;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 100rpx;
		display: flex;
		justify-content: flex-end;
		align-items: center;

		.right {
			width: 250rpx;
			text-align: center;
			padding: 0;
			height: 100%;
			line-height: 100rpx;
			border-radius: 0 !important;
			font-size: $font-size-lg;
			color: #FFFFFF;
		}
	}

	.customPay {
		height: 600rpx;
		width: 600rpx;
		background: #FFFFFF;
		display: flex;
		flex-direction: column;
		align-items: center;
		border-radius: 20rpx;
		position: relative;

		.btn_pay {
			width: 100%;
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			bottom: 30rpx;

			.btn {
				width: 80%;
				height: 100%;
				border-radius: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
</style>