/**
 * 全局UI管理器
 * 统一管理UI配置，避免重复请求，确保主题在所有页面生效
 */
class GlobalUIManager {
	constructor() {
		this.uiData = null
		this.isLoading = false
		this.loadPromise = null
		this.callbacks = []
		this.lastUpdateTime = 0
		this.cacheExpireTime = 5 * 60 * 1000 // 5分钟内不重复请求
		
		// 在小程序中使用全局数据存储
		if (typeof getApp === 'function') {
			const app = getApp()
			if (app) {
				app.globalData = app.globalData || {}
				app.globalData.uiConfig = null
			}
		}
	}

	// 获取UI配置（带缓存和去重）
	async getUIConfig(forceRefresh = false) {
		// 如果有缓存且未过期，直接返回
		if (!forceRefresh && this.uiData && this.isCacheValid()) {
			console.log('使用缓存的UI配置')
			return this.uiData
		}

		// 如果正在加载，返回现有的Promise
		if (this.isLoading && this.loadPromise) {
			console.log('UI配置加载中，等待现有请求')
			return this.loadPromise
		}

		// 开始新的加载
		this.isLoading = true
		this.loadPromise = this.loadUIConfig()

		try {
			const result = await this.loadPromise
			return result
		} finally {
			this.isLoading = false
			this.loadPromise = null
		}
	}

	// 实际加载UI配置
	async loadUIConfig() {
		try {
			console.log('开始加载UI配置...')
			
			// 这里需要在实际使用时替换为真实的请求方法
			// 暂时返回一个模拟的Promise，实际使用时会被替换
			const response = await this.makeUIRequest()
			
			if (response && response.data) {
				this.uiData = response.data
				this.lastUpdateTime = Date.now()
				this.updateGlobalData()
				this.notifyCallbacks('loaded', this.uiData)
				console.log('UI配置加载成功')
				return this.uiData
			} else {
				throw new Error('UI配置数据格式错误')
			}
		} catch (error) {
			console.error('加载UI配置失败:', error)
			this.notifyCallbacks('error', error)
			throw error
		}
	}

	// 设置请求方法（由外部注入）
	setRequestMethod(requestMethod) {
		this.makeUIRequest = requestMethod
	}

	// 检查缓存是否有效
	isCacheValid() {
		return Date.now() - this.lastUpdateTime < this.cacheExpireTime
	}

	// 应用UI配置到页面
	applyUIConfig(uiData = null) {
		const data = uiData || this.uiData
		if (!data) {
			console.warn('没有UI配置数据可应用')
			return
		}

		try {
			// 应用主题配置
			if (data.subject && data.subject.style) {
				this.applyTheme(data.subject.style)
			}

			// 应用版权信息
			if (data.copy_right) {
				this.applyCopyRight(data.copy_right)
			}

			// 应用控制组件配置
			if (data.control_component) {
				this.applyControlComponents(data.control_component)
			}

			console.log('UI配置应用成功')
			this.notifyCallbacks('applied', data)
		} catch (error) {
			console.error('应用UI配置失败:', error)
		}
	}

	// 应用主题
	applyTheme(themeStyle) {
		try {
			// 设置CSS变量或调用store方法
			if (typeof getApp === 'function') {
				const app = getApp()
				if (app && app.$store) {
					app.$store.dispatch('ui/toThemeColor', themeStyle)
				}
			}
			console.log('主题应用成功:', themeStyle)
		} catch (error) {
			console.error('应用主题失败:', error)
		}
	}

	// 应用版权信息
	applyCopyRight(copyRight) {
		try {
			if (typeof getApp === 'function') {
				const app = getApp()
				if (app && app.$store) {
					app.$store.dispatch('ui/toCopyRight', copyRight)
				}
			}
			console.log('版权信息应用成功')
		} catch (error) {
			console.error('应用版权信息失败:', error)
		}
	}

	// 应用控制组件
	applyControlComponents(components) {
		try {
			components.forEach(item => {
				if (item.sign === 'tab') {
					const tabbar = item.property.filter(item1 => item1.status === 1)
					if (typeof getApp === 'function') {
						const app = getApp()
						if (app && app.$store) {
							app.$store.dispatch('ui/toTabbar', tabbar)
						}
					}
				}

				if (item.sign === 'popover') {
					if (typeof getApp === 'function') {
						const app = getApp()
						if (app && app.$store) {
							app.$store.dispatch('ui/toPop', item.property)
						}
					}
				}
			})
			console.log('控制组件应用成功')
		} catch (error) {
			console.error('应用控制组件失败:', error)
		}
	}

	// 注册回调
	onStateChange(callback) {
		if (typeof callback === 'function') {
			this.callbacks.push(callback)
		}
		return this
	}

	// 移除回调
	offStateChange(callback) {
		const index = this.callbacks.indexOf(callback)
		if (index > -1) {
			this.callbacks.splice(index, 1)
		}
		return this
	}

	// 通知回调
	notifyCallbacks(type, data) {
		this.callbacks.forEach(callback => {
			try {
				callback(type, data)
			} catch (error) {
				console.error('UI回调执行失败:', error)
			}
		})
	}

	// 更新全局数据
	updateGlobalData() {
		if (typeof getApp === 'function') {
			const app = getApp()
			if (app && app.globalData) {
				app.globalData.uiConfig = this.uiData
			}
		}
	}

	// 从全局数据恢复
	restoreFromGlobalData() {
		if (typeof getApp === 'function') {
			const app = getApp()
			if (app && app.globalData && app.globalData.uiConfig) {
				this.uiData = app.globalData.uiConfig
				this.lastUpdateTime = Date.now() // 重置时间，避免立即过期
				console.log('从全局数据恢复UI配置')
				return true
			}
		}
		return false
	}

	// 强制刷新UI配置
	async refresh() {
		console.log('强制刷新UI配置')
		return await this.getUIConfig(true)
	}

	// 获取当前状态
	getStatus() {
		return {
			hasData: !!this.uiData,
			isLoading: this.isLoading,
			lastUpdateTime: this.lastUpdateTime,
			cacheValid: this.isCacheValid()
		}
	}

	// 清理资源
	cleanup() {
		this.uiData = null
		this.isLoading = false
		this.loadPromise = null
		this.callbacks = []
		this.lastUpdateTime = 0
	}
}

// 创建全局实例
const globalUIManager = new GlobalUIManager()

// 在小程序环境中，将管理器挂载到全局
if (typeof getApp === 'function') {
	setTimeout(() => {
		const app = getApp()
		if (app) {
			app.globalUIManager = globalUIManager
		}
	}, 0)
}

export default globalUIManager
