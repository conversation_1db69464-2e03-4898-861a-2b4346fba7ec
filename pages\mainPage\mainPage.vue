<template>
	<view class="main-container">
		<!-- 加载动画 -->
		<view v-if="isLoading" class="loading-container">
			<view class="loading-spinner">
				<view class="spinner"></view>
			</view>
			<text class="loading-text">正在加载酒店信息...</text>
		</view>

		<!-- 骨架屏 -->
		<view v-else-if="showSkeleton" class="skeleton-container">
			<view class="skeleton-avatar"></view>
			<view class="skeleton-text skeleton-title"></view>
		</view>

		<!-- 主要内容 -->
		<view v-else class="content-container" :class="{ 'fade-in': contentLoaded }">
			<image
				:src="hotel.pic_list && hotel.pic_list[0] ? hotel.pic_list[0] : '/static/images/hotelImg.png'"
				class="hotel-image"
				mode="aspectFill"
				@load="onImageLoad"
				@error="onImageError"
			></image>
			<text class="hotel-name">{{ hotel && hotel.shop_name ? hotel.shop_name : '酒店预定' }}</text>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				isLoading: true,
				showSkeleton: false,
				contentLoaded: false,
				imageLoaded: false
			}
		},
		components: {},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting','setting']),
		},
		watch: {
			// 监听酒店数据变化
			hotel: {
				handler(newVal) {
					if (newVal && newVal.shop_name) {
						this.checkContentReady();
					}
				},
				immediate: true
			}
		},
		async onLoad() {
			// 开始加载
			this.isLoading = true;

			try {
				await this.$onLaunched;

				// 显示骨架屏
				this.isLoading = false;
				this.showSkeleton = true;

				if(this.userInfo.block){
					this.showSkeleton = false;
					uni.showModal({
						title:'提示',
						content:'账号状态异常，请联系酒店处理!\n联系电话:'+this.hotel.link_phone,
						cancelText:'退出',
						confirmText:'联系酒店',
						success:(res)=> {
							if(res.confirm){
								uni.makePhoneCall({
									phoneNumber:this.hotel.link_phone
								})
							}else{
								uni.exitMiniProgram()
							}
						}
					})
					return
				}

				// 管理员判断逻辑
				this.checkAdminAccess();

			} catch (error) {
				console.error('页面加载失败:', error);
				this.isLoading = false;
				this.showSkeleton = false;
				this.contentLoaded = true;
			}
		},
		methods: {
			...mapActions('ui', ['toTabbar', 'toThemeColor', 'toPop', 'toCopyRight']),
			...mapActions('hotel', ['getHotelList', 'getHotel', 'getCityModel', 'getSaleTypes']),

			// 图片加载成功
			onImageLoad() {
				this.imageLoaded = true;
				this.checkContentReady();
			},

			// 图片加载失败
			onImageError() {
				this.imageLoaded = true;
				this.checkContentReady();
			},

			// 检查内容是否准备就绪
			checkContentReady() {
				if (this.hotel && this.imageLoaded) {
					this.showSkeleton = false;
					this.contentLoaded = true;
				}
			},

			// 检查管理员权限
			checkAdminAccess() {
				// 判断用户是否为管理员
				const isAdmin = this.isUserAdmin();

				if (isAdmin) {
					// 如果是管理员，显示弹窗询问是否进入管理界面
					this.goToAdminPanel();
					// uni.showModal({
					// 	title: '🔑 管理员权限检测',
					// 	content: '检测到您拥有管理员权限，是否直接进入酒店管理界面？',
					// 	cancelText: '客户模式',
					// 	confirmText: '管理模式',
					// 	success: (res) => {
					// 		if (res.confirm) {
					// 			// 用户选择进入管理界面
					// 			console.log('管理员选择进入管理模式');
					// 			this.goToAdminPanel();
					// 		} else {
					// 			// 用户选择继续使用普通界面
					// 			console.log('管理员选择继续使用客户端模式');
					// 			this.continueNormalFlow();
					// 		}
					// 	},
					// 	fail: () => {
					// 		// 弹窗失败时继续正常流程
					// 		console.log('管理员权限弹窗显示失败，继续正常流程');
					// 		this.continueNormalFlow();
					// 	}
					// });
				} else {
					// 不是管理员，继续正常流程
					this.continueNormalFlow();
				}
			},

			// 判断用户是否为管理员
			isUserAdmin() {
				// 检查用户信息是否存在
				console.log(this.userInfo,'mainpage',this.userInfo.is_boss);

				// 根据is_boss字段判断是否为管理员
				if (this.userInfo.is_boss) {
					console.log('通过is_boss字段判断为管理员:', this.userInfo.is_boss);
					return true;
				}

				console.log('is_boss字段值为:', this.userInfo.is_boss, '，判断为普通用户');
				return false;
			},

			// 进入管理员界面
			goToAdminPanel() {
				console.log('管理员选择进入管理界面');

				// 显示加载提示
				uni.showLoading({
					title: '正在进入管理模式...'
				});

				// 延迟一下，让用户看到加载提示
				setTimeout(() => {
					// 跳转到管理员页面
					// 这里需要根据实际的管理员页面路径来修改
					uni.reLaunch({
						url: '/packageA/manager/manager',
						success: () => {
							console.log('成功跳转到管理员界面');
							uni.hideLoading();
						},
						fail: (err) => {
							console.error('跳转管理员界面失败:', err);
							uni.hideLoading();
							uni.showToast({
								title: '跳转失败，请重试',
								icon: 'none'
							});
							// 跳转失败时继续正常流程
							this.continueNormalFlow();
						}
					});
				}, 500);
			},

			// 继续正常流程
			continueNormalFlow() {
				console.log('继续正常的页面流程');
				this.loadUIComponents();
			},

			// 加载UI组件（原来的逻辑）
			loadUIComponents() {
				// 获取全局UI
				this.$iBox.http('getUI', {})({
					method: 'post'
				}).then(res => {
					this.$iBox.http('getHomePageUi', {
						path: 'pages/index/index',
						shop_id: this.hotel.id
					})({
						method: 'post'
					}).then(res => {
						uni.hideLoading()
						// 判断是否是选择城市模式，判断组件
						res.data.forEach(item => {
							if (item.sign == 'book_room_1') {
								if (item.property.style == 4) {
									this.getCityModel(true)
								} else {
									this.getCityModel(false)
								}
							}
						})

						// 数据加载完成，检查内容是否准备就绪
						this.checkContentReady();
					})
					res.data.control_component.forEach(item => {
						if (item.sign == 'tab') {
							let tabbar = item.property.filter(item1 => {
								return item1.status == 1
							})
							if(tabbar.length > 0){
								uni.switchTab({
									url:'/'+tabbar[0].path
								})
							}
							this.toTabbar(tabbar)
						}

						if (item.sign == 'popover') {
							this.toPop(item.property)
						}
					})

					this.toThemeColor(res.data.subject.style)
					this.toCopyRight(res.data.copy_right)

				}).catch(err => {
					console.error('加载UI组件失败:', err);
					// 即使加载失败也要显示内容
					this.showSkeleton = false;
					this.contentLoaded = true;
				})
			},


		}
	}
</script>

<style scoped>
.main-container {
	margin-top: 360rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	min-height: 400rpx;
}

/* 加载动画样式 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 0;
}

.loading-spinner {
	margin-bottom: 30rpx;
}

.spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #f3f3f3;
	border-top: 6rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 骨架屏样式 */
.skeleton-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 0;
}

.skeleton-avatar {
	width: 260rpx;
	height: 260rpx;
	border-radius: 50%;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
}

.skeleton-text {
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	border-radius: 8rpx;
}

.skeleton-title {
	width: 300rpx;
	height: 40rpx;
	margin-top: 30rpx;
}

@keyframes skeleton-loading {
	0% {
		background-position: 200% 0;
	}
	100% {
		background-position: -200% 0;
	}
}

/* 内容样式 */
.content-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	opacity: 0;
	transform: translateY(20rpx);
	transition: all 0.5s ease-in-out;
}

.content-container.fade-in {
	opacity: 1;
	transform: translateY(0);
}

.hotel-image {
	width: 260rpx;
	height: 260rpx;
	border-radius: 50%;
	transition: transform 0.3s ease;
}

.hotel-image:hover {
	transform: scale(1.05);
}

.hotel-name {
	margin-top: 30rpx;
	font-size: 34rpx;
	font-weight: 700;
	text-align: center;
	color: #333;
	animation: fadeInUp 0.6s ease-out 0.3s both;
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.hotel-image {
		width: 200rpx;
		height: 200rpx;
	}

	.skeleton-avatar {
		width: 200rpx;
		height: 200rpx;
	}

	.hotel-name {
		font-size: 30rpx;
	}
}
</style>
